package sip

import (
	"testing"
	"regexp"
	"gb-gateway/internal/config"
	"gb-gateway/internal/state"
)

func TestGenerateGB28181SSRC(t *testing.T) {
	// Create test config
	cfg := &config.ServerConfig{
		SIPID: "34020000002000000001", // Standard 20-digit SIP ID
	}
	
	// Create test server
	stateManager := state.NewManager()
	srv := NewServer(cfg, stateManager)
	
	tests := []struct {
		name       string
		isRealTime bool
		wantRegex  string
	}{
		{
			name:       "Real-time stream",
			isRealTime: true,
			wantRegex:  `^0\d{9}$`, // Starts with 0, followed by 9 digits
		},
		{
			name:       "Historical stream", 
			isRealTime: false,
			wantRegex:  `^1\d{9}$`, // Starts with 1, followed by 9 digits
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ssrc := srv.generateGB28181SSRC(tt.isRealTime)
			
			// Check length
			if len(ssrc) != 10 {
				t.Errorf("SSRC length = %d, want 10", len(ssrc))
			}
			
			// Check format using regex
			matched, err := regexp.MatchString(tt.wantRegex, ssrc)
			if err != nil {
				t.Fatalf("Regex error: %v", err)
			}
			if !matched {
				t.Errorf("SSRC %s does not match pattern %s", ssrc, tt.wantRegex)
			}
			
			// Check domain ID extraction (should be "20000" from "34020000002000000001")
			expectedDomainID := "20000"
			actualDomainID := ssrc[1:6]
			if actualDomainID != expectedDomainID {
				t.Errorf("Domain ID = %s, want %s", actualDomainID, expectedDomainID)
			}
			
			t.Logf("Generated SSRC: %s", ssrc)
		})
	}
}

func TestGenerateGB28181SSRC_ShortSIPID(t *testing.T) {
	// Test with short SIP ID
	cfg := &config.ServerConfig{
		SIPID: "123", // Short SIP ID
	}
	
	stateManager := state.NewManager()
	srv := NewServer(cfg, stateManager)
	
	ssrc := srv.generateGB28181SSRC(true)
	
	// Should still be 10 digits
	if len(ssrc) != 10 {
		t.Errorf("SSRC length = %d, want 10", len(ssrc))
	}
	
	// Should start with 0 for real-time
	if ssrc[0] != '0' {
		t.Errorf("First digit = %c, want '0'", ssrc[0])
	}
	
	t.Logf("Generated SSRC with short SIP ID: %s", ssrc)
}

func TestGenerateGB28181SSRC_Uniqueness(t *testing.T) {
	cfg := &config.ServerConfig{
		SIPID: "34020000002000000001",
	}
	
	stateManager := state.NewManager()
	srv := NewServer(cfg, stateManager)
	
	// Generate multiple SSRCs and check they are different
	ssrcs := make(map[string]bool)
	for i := 0; i < 100; i++ {
		ssrc := srv.generateGB28181SSRC(true)
		if ssrcs[ssrc] {
			t.Errorf("Duplicate SSRC generated: %s", ssrc)
		}
		ssrcs[ssrc] = true
	}
	
	t.Logf("Generated %d unique SSRCs", len(ssrcs))
}
