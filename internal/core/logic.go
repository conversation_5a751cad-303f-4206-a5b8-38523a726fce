package core

import (
	"fmt"
	"log/slog"
	"time"

	"gb-gateway/internal/sip"
	"gb-gateway/internal/state"
	"gb-gateway/pkg/models"
)

// Logic represents core business logic
type Logic struct {
	stateManager *state.Manager
	sipServer    *sip.Server
	logger       *slog.Logger
}

// NewLogic creates a new core logic instance
func NewLogic(stateManager *state.Manager, sipServer *sip.Server) *Logic {
	return &Logic{
		stateManager: stateManager,
		sipServer:    sipServer,
		logger:       slog.Default(),
	}
}

// GetDevices retrieves real camera device list from memory cache
func (l *Logic) GetDevices(platformID string) ([]models.Device, error) {
	// If platformID is specified, use it; otherwise get first available platform
	if platformID == "" {
		platforms := l.stateManager.GetAllPlatforms()
		if len(platforms) == 0 {
			return nil, fmt.Errorf("no platforms registered")
		}
		platformID = platforms[0].ID
	}

	// Check if platform exists
	platform, exists := l.stateManager.GetPlatform(platformID)
	if !exists {
		return nil, fmt.Errorf("platform %s not found", platformID)
	}

	// Check if platform is still active (last seen within 5 minutes)
	if time.Since(platform.LastSeen) > 5*time.Minute {
		return nil, fmt.Errorf("platform %s not active", platformID)
	}

	// Always return cached real camera devices from memory
	cameras := l.stateManager.GetRealCameraDevices(platformID)

	return cameras, nil
}

// RequestStream requests video stream from device
func (l *Logic) RequestStream(gbID, receiveIP string, receivePort int) (*models.StreamResponse, error) {
	// Check if device exists
	device, exists := l.stateManager.GetDevice(gbID)
	if !exists {
		return nil, fmt.Errorf("device not found")
	}
	if !device.IsRealCamera() {
		return nil, fmt.Errorf("device is not a camera")
	}
	if device.Status != "ON" {
		return nil, fmt.Errorf("device is not online")
	}

	// Check if platform is active
	platform, exists := l.stateManager.GetPlatform(device.PlatformID)
	if !exists {
		return nil, fmt.Errorf("platform not found")
	}

	if time.Since(platform.LastSeen) > 5*time.Minute {
		return nil, fmt.Errorf("platform not active")
	}

	// Send INVITE request
	session, err := l.sipServer.SendInvite(gbID, receiveIP, receivePort)
	if err != nil {
		return nil, fmt.Errorf("failed to send INVITE: %w", err)
	}

	response := &models.StreamResponse{
		SSRC:      session.SSRC,
		SessionID: session.SessionID,
	}

	l.logger.Info("Stream requested", "gb_id", gbID, "session_id", session.SessionID)
	return response, nil
}

// ControlPTZ controls PTZ of device
func (l *Logic) ControlPTZ(gbID string, command models.PtzCmd, speed int) error {
	// Validate speed
	if speed < 0 || speed > 255 {
		return fmt.Errorf("invalid speed: %d (must be 0-255)", speed)
	}

	// Check if device exists
	device, exists := l.stateManager.GetDevice(gbID)
	if !exists {
		return fmt.Errorf("device not found")
	}

	// Check if platform is active
	platform, exists := l.stateManager.GetPlatform(device.PlatformID)
	if !exists {
		return fmt.Errorf("platform not found")
	}

	if time.Since(platform.LastSeen) > 5*time.Minute {
		return fmt.Errorf("platform not active")
	}

	// Send PTZ control command
	err := l.sipServer.SendPTZControl(gbID, command, speed)
	if err != nil {
		return fmt.Errorf("failed to send PTZ control: %w", err)
	}

	l.logger.Info("PTZ control sent", "gb_id", gbID, "command", command, "speed", speed)
	return nil
}

// StopStream stops video stream
func (l *Logic) StopStream(sessionID string) error {
	_, exists := l.stateManager.GetSession(sessionID)
	if !exists {
		return fmt.Errorf("session not found")
	}

	// Send BYE request to stop stream
	err := l.sipServer.SendBye(sessionID)
	if err != nil {
		return fmt.Errorf("failed to send BYE: %w", err)
	}

	l.logger.Info("Stream stopped", "session_id", sessionID)
	return nil
}

// GetSessionInfo gets session information
func (l *Logic) GetSessionInfo(sessionID string) (*models.StreamSession, error) {
	session, exists := l.stateManager.GetSession(sessionID)
	if !exists {
		return nil, fmt.Errorf("session not found")
	}

	return session, nil
}

// GetPlatformStatus gets platform status
func (l *Logic) GetPlatformStatus() ([]*models.Platform, error) {
	platforms := l.stateManager.GetAllPlatforms()
	return platforms, nil
}
